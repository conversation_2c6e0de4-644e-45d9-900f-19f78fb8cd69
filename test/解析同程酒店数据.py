#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同程酒店数据解析脚本
功能：解析同程酒店数据，保持原有结构，但只保留指定字段
"""

import json
import os
from typing import Dict, List, Any


class HotelDataParser:
    """酒店数据解析器"""
    
    def __init__(self, input_file_path: str, output_file_path: str):
        """
        初始化解析器
        
        Args:
            input_file_path: 输入文件路径
            output_file_path: 输出文件路径
        """
        self.input_file_path = input_file_path
        self.output_file_path = output_file_path
        self.original_data = None
        self.parsed_data = None
    
    def load_data(self) -> bool:
        """
        加载原始数据
        
        Returns:
            bool: 加载是否成功
        """
        try:
            with open(self.input_file_path, 'r', encoding='utf-8') as file:
                self.original_data = json.load(file)
            return True
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def parse_list_rate_plan(self, rate_plan: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析单个ratePlan，只保留指定字段
        
        Args:
            rate_plan: 原始ratePlan数据
            
        Returns:
            Dict[str, Any]: 解析后的ratePlan数据
        """
        # 只保留指定的字段
        required_fields = ['cNRatePlanName', 'hotelID', 'hotelName', 'ratePlanID', 'roomTypeID']
        parsed_rate_plan = {}
        
        for field in required_fields:
            if field in rate_plan:
                parsed_rate_plan[field] = rate_plan[field]
        
        return parsed_rate_plan
    
    def parse_price_item(self, price_item: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析单个价格项，只保留指定字段
        
        Args:
            price_item: 原始价格数据
            
        Returns:
            Dict[str, Any]: 解析后的价格数据
        """
        # 只保留指定的字段
        required_fields = ['genSaleCost', 'ratePlanID', 'roomTypeID', 'currentDateStr']
        parsed_price_item = {}
        
        for field in required_fields:
            if field in price_item:
                parsed_price_item[field] = price_item[field]
        
        return parsed_price_item
    
    def parse_price_map(self, price_map: Dict[str, List[Dict[str, Any]]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        解析priceMap，只保留指定字段
        
        Args:
            price_map: 原始priceMap数据
            
        Returns:
            Dict[str, List[Dict[str, Any]]]: 解析后的priceMap数据
        """
        parsed_price_map = {}
        
        for rate_plan_id, price_list in price_map.items():
            parsed_price_list = []
            for price_item in price_list:
                parsed_price_item = self.parse_price_item(price_item)
                parsed_price_list.append(parsed_price_item)
            parsed_price_map[rate_plan_id] = parsed_price_list
        
        return parsed_price_map
    
    def parse_data(self) -> bool:
        """
        解析数据，保持原有结构但只保留指定字段
        
        Returns:
            bool: 解析是否成功
        """
        if not self.original_data:
            print("请先加载数据")
            return False
        
        try:
            # 创建新的数据结构，保持原有结构
            self.parsed_data = {
                "data": {
                    "listRatePlan": [],
                    "priceMap": {}
                }
            }
            
            # 解析listRatePlan
            list_rate_plan = self.original_data.get("data", {}).get("listRatePlan", [])
            for rate_plan in list_rate_plan:
                parsed_rate_plan = self.parse_list_rate_plan(rate_plan)
                self.parsed_data["data"]["listRatePlan"].append(parsed_rate_plan)
            
            # 解析priceMap
            price_map = self.original_data.get("data", {}).get("priceMap", {})
            self.parsed_data["data"]["priceMap"] = self.parse_price_map(price_map)
            
            return True
            
        except Exception as e:
            print(f"解析数据失败: {e}")
            return False
    
    def save_data(self) -> bool:
        """
        保存解析后的数据
        
        Returns:
            bool: 保存是否成功
        """
        if not self.parsed_data:
            print("请先解析数据")
            return False
        
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(self.output_file_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            with open(self.output_file_path, 'w', encoding='utf-8') as file:
                json.dump(self.parsed_data, file, ensure_ascii=False, indent=4)
            
            print(f"数据已保存到: {self.output_file_path}")
            return True
            
        except Exception as e:
            print(f"保存数据失败: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, int]:
        """
        获取解析统计信息
        
        Returns:
            Dict[str, int]: 统计信息
        """
        if not self.parsed_data:
            return {}
        
        stats = {
            "listRatePlan_count": len(self.parsed_data["data"]["listRatePlan"]),
            "priceMap_ratePlan_count": len(self.parsed_data["data"]["priceMap"]),
            "total_price_items": sum(len(price_list) for price_list in self.parsed_data["data"]["priceMap"].values())
        }
        
        return stats
    
    def run(self) -> bool:
        """
        执行完整的解析流程
        
        Returns:
            bool: 执行是否成功
        """
        print("开始解析同程酒店数据...")
        
        # 加载数据
        if not self.load_data():
            return False
        
        # 解析数据
        if not self.parse_data():
            return False
        
        # 保存数据
        if not self.save_data():
            return False
        
        # 显示统计信息
        stats = self.get_statistics()
        print("解析完成！统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        return True


def main():
    """主函数"""
    # 文件路径配置
    input_file = "同程酒店数据3.json"
    output_file = "解析后的同程酒店数据.json"
    
    # 创建解析器并执行
    parser = HotelDataParser(input_file, output_file)
    success = parser.run()
    
    if success:
        print("数据解析完成！")
    else:
        print("数据解析失败！")


if __name__ == "__main__":
    main() 