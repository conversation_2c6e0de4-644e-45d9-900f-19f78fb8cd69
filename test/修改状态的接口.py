
import requests


import subprocess  # 执行命令行的
from functools import partial  # 固定某个参数的
subprocess.Popen = partial(subprocess.Popen, encoding='utf-8')
import execjs
import json

# 上面的导入顺序不能变



headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "baggage": "sentry-environment=production,sentry-release=EBK-PC-1.139.0,sentry-public_key=8a4553a20fe54c7cbb365f634d8046f0,sentry-trace_id=7aabca95216c47fa876919979ea46467,sentry-sample_rate=0.1,sentry-transaction=%2Febk-product%2F%3ApathMatch(.*)*,sentry-sampled=false",
    "cache-control": "no-cache",
    "content-type": "application/json",
    "origin": "https://ebooking.elong.com",
    "pragma": "no-cache",
    "priority": "u=1, i",
    "referer": "https://ebooking.elong.com/ebkcommon/dashboard",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sentry-trace": "7aabca95216c47fa876919979ea46467-82a0195c30f2bda2-0",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "user-dun": "x77xxxxxIGgZiGtxIigRDV6lc8hZzxCYhl8dWX7o85pxEzK050SIi80Mx70ntToXx7xRIUhqhAy26THOxkMG6TTOoBOy50tMMGkjGcxdlivNH6K1+2my86K1Oi6ytO7AqikJGQxkMalN8h5XOavE60p6oBOy50tMMGkjGcxdlivNH6K1+2my86K1Oi6ytO7AqikJGQxkMalN8h5XOavE6Jp6ItNtix9OxxRGEQukiHy86qZliHhtys3OoiSxeoFwVk+9c1vqq4p76nunxkNtmS04cqu56vwIDz5E4/RuSou/MugngonMM/vFgMEMd/MyebFM60APikNG6sZO67PGYHNnU3pCHgySIad4c8Yq2q5GIMoToxh7V+jlfDu+ctCAu8s8g9NLVk9hv2g9nki7WkbTzkHVHSjqx3nt+Vv6YwKG6Yp9oHitAHPglfLiFa0BZjBUriMOr9U83fQB7uuErC6BfYnhmSzgf920FgM3mWDiSSjf7u1xE56MKfLiOuJQpmnESxhfPPLiTSr6KgL2TBVQbfLzcGe3LmEGyHKA4J/5/Cz6MY15NRrQrR12TiWY2gBEXtROeKBj1w4OakS0x5PCisxH"
}
cookies = {
    "H5CookieId": "516c75a9-bdfa-49d5-ac16-02bf48977d3e",
    "firsttime": "1747797257645",
    "EbkSessionId": "283e10cdaaf342f695142b9d591a0e49",
    "x-hng": "lang=zh-CN&domain=ebooking.elong.com",
    "socketNextConnectTryTime": "",
    "socketTimePause": "3",
    "language": "zh_CN",
    "route": "f991d267777d8cbaa3a8de3535a46674",
    "lasttime": "1753081016074"
}
url = "https://ebooking.elong.com/product/roomPrice/setPriceStatus"
data = {
    "hotelID": "29200198",
    "endTime": "2025-07-24",
    "startTime": "2025-07-24",
    "isEffective": False,
    "ratePlanID": 439377578,
    "roomTypeID": "0004"
}


with open('./同程旅行机票.js', 'r', encoding='utf-8') as f:
    js_file = f.read()


js_file = js_file.replace("所有的data", json.dumps(data).replace(" ", ''))
js_file = js_file.replace('当前url', url)
user_dun = execjs.compile(js_file).call('run')
print(user_dun)
headers['user-dun'] = user_dun['value']

data = json.dumps(data, separators=(',', ':'))
response = requests.post(url, headers=headers, cookies=cookies, data=data)

print(response.text)
print(response)