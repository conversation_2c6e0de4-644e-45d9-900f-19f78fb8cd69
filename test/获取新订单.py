import requests
import json


import subprocess  # 执行命令行的
from functools import partial  # 固定某个参数的
subprocess.Popen = partial(subprocess.Popen, encoding='utf-8')
import execjs
import json

headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "baggage": "sentry-environment=production,sentry-release=EBK-PC-1.139.0,sentry-public_key=8a4553a20fe54c7cbb365f634d8046f0,sentry-trace_id=bff739c8136041b8be69202dc4ff70c9,sentry-sample_rate=0.1,sentry-transaction=%2Febk-order%2F%3ApathMatch(.*)*,sentry-sampled=true",
    "cache-control": "no-cache",
    "content-type": "application/json",
    "origin": "https://ebooking.elong.com",
    "pragma": "no-cache",
    "priority": "u=1, i",
    "referer": "https://ebooking.elong.com/ebkcommon/dashboard",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sentry-trace": "bff739c8136041b8be69202dc4ff70c9-9be68c9a23533710-1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "user-dun": "x77xxxxxIDZhMRjxIYDfJday9ijjzxCYhl8dWX7o85pxEzK050SIi80Mx70ntTo7x7xRHdGnxVk7E6IU+IZ7E6IJ+89Peka+vCFEoxq8Vj7zq5/JajPEq5/JCjs0Xx1ytm7EFI18RCst+H9ia8sy65BJ+89Peka+vCFEoxq8Vj7zq5/JajPEq5/JCjs0Xx1ytm7EFI18RCst+H9ia8sy6557+wFxVmIU6w3cs/nNMIa7tmK7OBayCWxKGjxNQVdX+iZ7YQtt+myHjPj7OBZ7ToupgoVmlXsi+aLM2MrdTpm4jNrWWp6v36JWWp+l3X0WdeR/66NU+I97CmxUOrl117jEB4wZ1KyBAe+Hp0kmmpXSnUQpQkiyTVL7Bz+0Mv6N5kNosDLp43s9biEsWS2u0tDmvkxGbk46en3110kmnMjbkvKp2giy0YmsSc2U4bm5nm7FFH1tCSgycxZPCkB1mILW2As04w9PYZ70nYkcgkIrDt4+GfB13YrSrFV147POgz2n9Yjc0AxnX4rprsF19h8mfqfa40PmBS3iH8Qp0AFAW8K/rLik44u7M2Iy9KjLnzluG4cBM+f7Vj78p5cYa7Pt"
}
cookies = {
    "H5CookieId": "516c75a9-bdfa-49d5-ac16-02bf48977d3e",
    "firsttime": "1747797257645",
    "EbkSessionId": "283e10cdaaf342f695142b9d591a0e49",
    "x-hng": "lang=zh-CN&domain=ebooking.elong.com",
    "socketNextConnectTryTime": "",
    "socketTimePause": "3",
    "language": "zh_CN",
    "route": "7f6557577e2e581ce117ae5d010943e0",
    "lasttime": "1753087682886"
}


with open('./同程旅行机票.js', 'r', encoding='utf-8') as f:
    js_file = f.read()




url = "https://ebooking.elong.com/ebkorder/searchOrder/getTodayNewOrders"
data = {
    "pageNum": 1,
    "pageSize": 10,
    "paymentType": 0,
    "guaranteeType": 0,
    "provinceId": "",
    "filterHotelId": "",
    "beginDateStr": "2025-07-21 00:00:00",
    "endDateStr": "2025-07-21 23:59:59",
    "downloadType": 1
}

js_file = js_file.replace("所有的data", json.dumps(data).replace(" ", ''))
js_file = js_file.replace('当前url', url)
user_dun = execjs.compile(js_file).call('run')
print(user_dun)
headers['user-dun'] = user_dun['value']

data = json.dumps(data, separators=(',', ':'))
response = requests.post(url, headers=headers, cookies=cookies, data=data)

print(response.text)
print(response)

