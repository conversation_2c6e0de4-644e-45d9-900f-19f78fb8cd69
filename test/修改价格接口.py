import requests
import json
import requests

import subprocess  # 执行命令行的
from functools import partial  # 固定某个参数的
subprocess.Popen = partial(subprocess.Popen, encoding='utf-8')

import execjs
import json

headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "baggage": "sentry-environment=production,sentry-release=EBK-PC-1.139.0,sentry-public_key=8a4553a20fe54c7cbb365f634d8046f0,sentry-trace_id=b9397168ffd747148bab2476b5138a82,sentry-sample_rate=0.1,sentry-transaction=%2Febk-product%2F%3ApathMatch(.*)*,sentry-sampled=false",
    "cache-control": "no-cache",
    "content-type": "application/json",
    "origin": "https://ebooking.elong.com",
    "pragma": "no-cache",
    "priority": "u=1, i",
    "referer": "https://ebooking.elong.com/ebkcommon/dashboard",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sentry-trace": "b9397168ffd747148bab2476b5138a82-90f2a7c8e7ee3a07-0",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "user-dun": "x77xxxxxI9Vy4hSxIGTJuEqLPF+PzxCYhl8dWX7o85pxEzK050SIi80Mx70ntToMx7xtGWcUpwH50mSTWsMi0NthUBSLhaSMQgNo03WjQ7xzMqhsWNDgJTw/7fRHIXw+WxhoRXbHWB+zqOKiDSyD7TbKEgSobqbEDfOUFiKab7UDJzWSDfwkay6SECmGGzck4q/PNFbOE2LPhag0wsOiCgxNXzOtnP2uDgiG6pzU7MEaKG6Ux5CVbxKT7EIfwzP4bxDw7Ytw4wd3tiuA36QSxOY8IF/gYVcpKsdBXKzX9XcdKhGSx5tYFUksFjfGciGSi795c5ShLBqzYJ4rRVazPserVv+bAwfcVGpbAI3JWANbAIZ2Vcclxkx2xDX07Nc17W2qVJDtmrZfc7yNGHq4P6p3ObOzOlcELd33Y0hN15XfCJ0RvBugdeWfp33VzrRwgxkf2TgqBLbe7ZfG0pWLQPMHrXUCqfzx0CrpFluI0CrpBlO15pwLiZgPSzrMufLFgRUp2TgqBLbe7ZfG0pWLQPMHrXUCqfzx0CrpFluI0CrpBlO15pwLiZgPSzrMufnrUHFpJJG+UPaVfCPoh9oFI78Ya5jVi75="
}
cookies = {
    "H5CookieId": "516c75a9-bdfa-49d5-ac16-02bf48977d3e",
    "firsttime": "1753020866390",  # 当前时间：2025-07-20 22:14:26
    "EbkSessionId": "283e10cdaaf342f695142b9d591a0e49",
    "x-hng": "lang=zh-CN&domain=ebooking.elong.com",
    "socketNextConnectTryTime": "",
    "socketTimePause": "3",
    "language": "zh_CN",
    "route": "1754eab5519eed1cbed1f94e8cdcc1e7",
    "lasttime": "1753625666390"  # 一周后时间：2025-07-27 22:14:26
}
url = "https://ebooking.elong.com/product/roomPrice/ajaxSavePrice"


data = {
    "hotelID": "27105890",
    "currencyCode": "HKD",
    "customWeekdaySum": 0,
    "startDate": "2025-08-04",
    "endDate": "2025-08-04",
    "priceInfoList": [
        {
            "addBedPrice": 0,
            "addValue": "11.11",
            "allowAddBed": 0,
            "commissionTypeLocal": 1,
            "commissionValue": "",
            "computingMethodLocal": "3",
            "isEffective": 1,
            "maxProfit": "",
            "minProfit": "",
            "netValue": "",
            "productID": {
                "hotelID": "27105890",
                "roomTypeID": "0005",
                "ratePlanID": 445705674
            },
            "jingjiayongjinlv": "",
            "saleCost": 3916,
            "salePrice": "",
            "settlementtype": 2,
            "beginDate": 1754236800000,
            "endDate": 1754236800000,
            "beginTime": "2025.08.04",
            "endTime": "2025.08.04",
            "currencyCode": "HKD"
        }
    ],
    "priceSubmitBatchID": "",
    "priceTypeLocal": "0",
    "weekDays": "Monday"
}



with open('./同程旅行机票.js', 'r', encoding='utf-8') as f:
    js_file = f.read()
js_file = js_file.replace("所有的data", json.dumps(data).replace(" ", ''))
js_file = js_file.replace('当前url', url)
user_dun = execjs.compile(js_file).call('run')
print(user_dun)
headers['user-dun'] = user_dun['value']

data = json.dumps(data, separators=(',', ':'))
response = requests.post(url, headers=headers, cookies=cookies, data=data)

print(response.text)
print(response)
