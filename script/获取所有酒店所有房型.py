import json

import csv

import time

import logging

from tongcheng.hotel_data.hotel_operator import TongchengHotelOperator

from tongcheng.config.browser_config import BrowserConfig

from DrissionPage import ChromiumPage, ChromiumOptions

from tongcheng.hotel_data.send_hotel_request import TongchengHotelRequestSender

from tongcheng.hotel_data.get_roomid import RoomRelationshipGetter





class TongchengAllHotelsDataGetter(TongchengHotelOperator):

    """

    获取所有酒店房型数据的类

    基于TongchengHotelOperator扩展，支持批量处理多个酒店

    """

    

    def __init__(self, logger, page=None):

        super().__init__(page, logger)

        self.logger = logger

        self.co = ChromiumOptions()

        self.co.headless(BrowserConfig.headless)

        self.hotel_operator = None

        self.base_url = "https://ebooking.elong.com"

        self.dashboard_url = "https://ebooking.elong.com/ebkcommon/dashboard#/dashboard"

        self.cookies_path = BrowserConfig.cookies_path

        self.hotel_request = None

        self.room_relationship_getter = None

        self.accumulated_result = {}

        self.csv_file_path = "resource/道旅酒店_八大洲平台匹配_已上架.csv"



    def connect(self):

        """

        连接到同程系统并初始化相关组件

        """

        self.logger.info("正在连接到同程系统...")

        self.page = ChromiumPage(self.co)

        

        # 加载cookies

        with open(self.cookies_path, "r") as f:

            cookies = json.load(f)

        

        self.page.get(self.base_url)

        self.page.set.cookies(cookies)

        self.page.get(self.dashboard_url)

        

        # 初始化相关组件

        self.hotel_operator = TongchengHotelOperator(self.page, self.logger)

        self.hotel_request = TongchengHotelRequestSender(logger=self.logger)

        self.room_relationship_getter = RoomRelationshipGetter(logger=self.logger)

        

        self.logger.info("连接成功")



    def load_hotel_list_from_csv(self):

        """

        从CSV文件加载酒店列表

        

        Returns:

            list: 包含酒店信息的字典列表

        """

        hotel_list = []

        try:

            with open(self.csv_file_path, 'r', encoding='utf-8') as f:

                reader = csv.DictReader(f)

                for row in reader:

                    hotel_info = {

                        'hotel_id': row['同程酒店编码'],

                        'hotel_name': row['同程酒店名称'],

                        'badazhou_code': row['八大洲酒店编码'],

                        'badazhou_name': row['八大洲酒店名称']

                    }

                    hotel_list.append(hotel_info)

            

            self.logger.info(f"从CSV文件加载了 {len(hotel_list)} 个酒店")

            return hotel_list

            

        except Exception as e:

            self.logger.error(f"加载CSV文件失败: {str(e)}")

            raise



    def get_all_hotels_data(self, days: int = 14):

        """

        获取所有酒店的房型数据

        

        Args:

            days: 查询天数，默认14天

        """

        # 重置累积数据

        self.reset_accumulated_data()

        

        # 加载酒店列表

        hotel_list = self.load_hotel_list_from_csv()

        

        # 设置查询日期

        start_date = time.strftime("%Y-%m-%d")

        end_date = time.strftime(

            "%Y-%m-%d",

            time.localtime(time.time() + days * 24 * 60 * 60)

        )

        

        self.logger.info(f"开始获取所有酒店房型数据，查询日期: {start_date} 到 {end_date}")

        

        # 遍历处理每个酒店

        for index, hotel_info in enumerate(hotel_list, 1):

            try:

                self.logger.info(f"正在处理第 {index}/{len(hotel_list)} 个酒店: {hotel_info['hotel_name']}")

                

                # 获取单个酒店的房型数据

                self.get_single_hotel_data(

                    hotel_id=hotel_info['hotel_id'],

                    hotel_name=hotel_info['hotel_name'],

                    start_date=start_date,

                    end_date=end_date

                )

                

                # 添加延迟避免请求过于频繁

                time.sleep(2)

                

            except Exception as e:

                self.logger.error(f"处理酒店 {hotel_info['hotel_name']} 时发生错误: {str(e)}")

                continue

        

        # 保存最终结果

        self.save_final_result()

        

        self.logger.info("所有酒店房型数据获取完成")



    def get_single_hotel_data(self, hotel_id: str, hotel_name: str, start_date: str, end_date: str):

        """

        获取单个酒店的房型数据

        

        Args:

            hotel_id: 酒店ID

            hotel_name: 酒店名称

            start_date: 开始日期

            end_date: 结束日期

        """

        try:

            # 获取房型列表

            room_list, room_name_map = self.room_relationship_getter.get_room_list(

                hotel_id, start_date, end_date

            )

            

            if not room_list:

                self.logger.warning(f"酒店 {hotel_name} 未找到房型数据")

                return

            

            self.logger.info(f"酒店 {hotel_name} 找到 {len(room_list)} 个房型")

            

            # 处理每个房型

            for room in room_list:

                self.logger.info(f"正在获取房型 {room} 的数据...")

                

                response_data = self.hotel_request.get_hotel_price(

                    hotel_id=hotel_id,

                    room_type=room,

                    start_date=start_date,

                    end_date=end_date,

                    save_to_file=True

                )

                

                # 解析房型数据

                self.parse_room_list_from_response(response_data, room_name_map, hotel_id)

                

                # 添加短暂延迟

                time.sleep(1)

                

        except Exception as e:

            self.logger.error(f"获取酒店 {hotel_name} 数据失败: {str(e)}")

            raise



    def parse_room_list_from_response(self, response_data, room_name_map, hotel_id):

        """

        解析响应数据中的房型信息和价格计划信息

        

        Args:

            response_data: 酒店价格查询的响应数据

            room_name_map: 房型名称映射

            hotel_id: 酒店ID

        """

        try:

            # 从响应数据中获取房型信息

            data = response_data.get('data', {})

            list_rate_plan = data.get('listRatePlan', [])

            

            if not list_rate_plan:

                self.logger.warning(f"酒店 {hotel_id} 未找到价格计划数据")

                return

            

            # 确保酒店ID存在于累积结果中

            if hotel_id not in self.accumulated_result:

                self.accumulated_result[hotel_id] = {}

            

            # 按房型ID组织数据

            room_rateplan_map = {}

            

            # 遍历价格计划，按房型分组

            for rate_plan in list_rate_plan:

                room_id = rate_plan.get('roomTypeID', '')

                rateplan_id = rate_plan.get('ratePlanID', '')

                rateplan_name = rate_plan.get('cNRatePlanName', '')

                

                if room_id not in room_rateplan_map:

                    room_rateplan_map[room_id] = []

                

                room_rateplan_map[room_id].append({

                    'rateplanid': rateplan_id,

                    'rateplanname': rateplan_name

                })

            

            # 为每个房型创建或更新数据结构

            for room_id, rateplans in room_rateplan_map.items():

                if room_id not in self.accumulated_result[hotel_id]:

                    # 获取真实的房型名称，如果不存在则使用默认名称

                    room_name = room_name_map.get(room_id, f"房型{room_id}")

                    

                    self.accumulated_result[hotel_id][room_id] = {

                        'room_id': room_id,

                        'room_name': room_name,

                        'room_rateplan_list': {}

                    }

                

                # 添加价格计划信息

                for index, rateplan in enumerate(rateplans):

                    self.accumulated_result[hotel_id][room_id]['room_rateplan_list'][str(index)] = {

                        'rateplanid': rateplan['rateplanid'],

                        'rateplanname': rateplan['rateplanname']

                    }

            

            self.logger.info(f"酒店 {hotel_id} 房型数据解析完成")

            

        except Exception as e:

            self.logger.error(f"解析房型列表响应数据失败: {str(e)}")

            raise



    def reset_accumulated_data(self):

        """

        重置累积的数据，开始新的酒店数据获取

        """

        self.accumulated_result = {}

        self.logger.info("已重置累积数据，开始新的酒店数据获取")



    def save_final_result(self):

        """

        保存最终结果到文件

        """

        try:

            output_file = 'all_hotels_room_relationship.json'

            with open(output_file, 'w', encoding='utf-8') as f:

                json.dump(self.accumulated_result, f, ensure_ascii=False, indent=4)

            

            self.logger.info(f"所有酒店房型数据已保存到: {output_file}")

            self.logger.info(f"共处理了 {len(self.accumulated_result)} 个酒店的数据")

            

            # 打印统计信息

            total_rooms = sum(len(hotel_data) for hotel_data in self.accumulated_result.values())

            self.logger.info(f"总共获取了 {total_rooms} 个房型的数据")

            

        except Exception as e:

            self.logger.error(f"保存最终结果失败: {str(e)}")

            raise





if __name__ == "__main__":

    # 配置日志

    logging.basicConfig(

        level=logging.INFO,

        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'

    )

    logger = logging.getLogger(__name__)

    

    # 创建酒店数据获取器实例

    all_hotels_getter = TongchengAllHotelsDataGetter(logger, page=None)

    

    try:

        # 连接系统

        all_hotels_getter.connect()

        

        # 获取所有酒店房型数据（默认查询14天）

        all_hotels_getter.get_all_hotels_data(days=14)

        

    except Exception as e:

        logger.error(f"程序执行失败: {str(e)}")

        raise

