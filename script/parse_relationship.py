import json
import os

def parse_hotel_relationship():
    """
    解析酒店房型关系数据和同程酒店数据，生成类似relationship.sjon的数据结构
    """
    
    # 读取酒店房型关系数据
    with open('酒店房型关系数据.json', 'r', encoding='utf-8') as f:
        relationship_data = json.load(f)
    
    # 读取同程酒店数据
    with open('同程酒店数据.json', 'r', encoding='utf-8') as f:
        hotel_data = json.load(f)
    
    # 创建结果数据结构
    result = {}
    
    # 从房型关系数据中获取房型信息
    room_response = relationship_data.get('data', {}).get('roomResponse', [])
    rp_response = relationship_data.get('data', {}).get('rpResponse', [])
    
    # 从同程酒店数据中获取价格计划信息
    list_rate_plan = hotel_data.get('data', {}).get('listRatePlan', [])
    
    # 获取酒店ID
    hotel_id = relationship_data.get('data', {}).get('shotelSupplierInfo', {}).get('mHotelID', '')
    
    # 按酒店ID组织数据
    for room_info in room_response:
        room_id = room_info.get('roomID', '')
        room_name = room_info.get('roomName', '')
        
        if hotel_id not in result:
            result[hotel_id] = {}
        
        if room_id not in result[hotel_id]:
            result[hotel_id][room_id] = {
                'room_id': room_id,
                'room_name': room_name,
                'room_rateplan_list': {}
            }
        
        # 查找该房型对应的价格计划
        rateplan_index = 0
        for rate_plan in rp_response:
            if rate_plan.get('roomTypeID') == room_id:
                rateplan_id = rate_plan.get('ratePlanID', '')
                rateplan_name = rate_plan.get('cNRatePlanName', '')
                
                result[hotel_id][room_id]['room_rateplan_list'][str(rateplan_index)] = {
                    'rateplanid': rateplan_id,
                    'rateplanname': rateplan_name
                }
                rateplan_index += 1
        
        # 如果没有找到价格计划，也从同程酒店数据中查找
        if rateplan_index == 0:
            for rate_plan in list_rate_plan:
                if rate_plan.get('roomTypeID') == room_id:
                    rateplan_id = rate_plan.get('ratePlanID', '')
                    rateplan_name = rate_plan.get('cNRatePlanName', '')
                    
                    result[hotel_id][room_id]['room_rateplan_list'][str(rateplan_index)] = {
                        'rateplanid': rateplan_id,
                        'rateplanname': rateplan_name
                    }
                    rateplan_index += 1
    
    # 保存结果到文件
    output_file = 'parsed_relationship.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=4)
    
    print(f"解析完成，结果已保存到: {output_file}")
    print(f"共解析了 {len(result)} 个酒店的数据")
    
    # 打印示例数据
    if result:
        first_hotel = list(result.keys())[0]
        first_room = list(result[first_hotel].keys())[0]
        print(f"\n示例数据结构:")
        print(f"酒店ID: {first_hotel}")
        print(f"房型ID: {first_room}")
        print(f"房型名称: {result[first_hotel][first_room]['room_name']}")
        print(f"价格计划数量: {len(result[first_hotel][first_room]['room_rateplan_list'])}")
    
    return result

if __name__ == "__main__":
    parse_hotel_relationship() 