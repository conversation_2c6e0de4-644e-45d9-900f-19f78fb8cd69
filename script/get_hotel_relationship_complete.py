import requests
import json
import subprocess
from functools import partial
subprocess.Popen = partial(subprocess.Popen, encoding='utf-8')
import execjs
import time

def get_room_relationship_data():
    """
    获取酒店房型关系数据
    """
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9",
        "baggage": "sentry-environment=production,sentry-release=EBK-PC-1.139.0,sentry-public_key=8a4553a20fe54c7cbb365f634d8046f0,sentry-trace_id=c0cf655813754302a27905189d4d2c00,sentry-sample_rate=0.1,sentry-transaction=%2Febk-product%2F%3ApathMatch(.*)*,sentry-sampled=false",
        "content-type": "application/json",
        "origin": "https://ebooking.elong.com",
        "priority": "u=1, i",
        "referer": "https://ebooking.elong.com/ebkcommon/dashboard",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "sentry-trace": "c0cf655813754302a27905189d4d2c00-b9342c53d70ceb6e-0",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
        "user-dun": "x77xxxxxIG2Av5txItboHWYTZ1SozxCYhl8dWX7o85pxfFHtvk3hVyzTxNF7uu0wx7xCTcqivhknFAPieSannAZ2ejV67HoNy7xthWsxaiIjStF+Zax8StqNqixj/GF7X8x8xis7Y9Uctho5lJajK4q2ejV67HoNy7xthWsxaiIjStF+Zax8StqNqixj/GF7X8x8xis7Y9Uctho5lJajK4sKckWL7HZmRvQQMi37YBanS0Pxn5auCHPg3lxhCWgp2jinIEPZFzU5vpxxnSancg9tYe2t8EFxeP+ytx4ID3xVKK4BLqxzO89BLeYfp0QgK6Udddtxe5anxHP7n5xR5KwPc/QLECdQJHA4YLj9ZOplywoMoAZO+l2hAa7N+qaQfQ8l81dcQgqoSHBQNbXvMbOT+Rw4CokAoLT4A6rNyrKOanz3oH1+h95oJaBlJI2lAOesQq5KYkiqm157oIah8WLAoIeq0RBvJ9OXgW2MvdsXMapOgMzwhM3TVLcQeQgHydOMOv6lJqpde63Joe3QeQk+zdAQz92+AfslMHnFodV6edVbgHAleQzyg9h6OAT2ngO6QqyId1qApGZ7izIEx7Kxi7=="
    }
    
    cookies = {
        "socketTimePause": "3",
        "socketNextConnectTryTime": "",
        "viewLpsRemind": "null",
        "creditAuditStatus": "null",
        "isShowTaxTips": "null",
        "language": "zh_CN",
        "EbkSessionId": "6c8a74f8c9ba4e2d8790d66c79e792f5",
        "JSESSIONID": "FAAFADF665BC8F642470EC5B9479F7A4",
        "firsttime": "1748366873016",
        "route": "19ef036f19f3784cd71c484acca86aa8",
        "lasttime": "1753264795432"
    }

    url = "https://ebooking.elong.com/product/roomPrice/ajaxRoomRateList"
    data = {
        "hotelId": "26964091",
        "productType": 0,
        "startDate": "2025-07-23",
        "endDate": "2025-08-05"
    }

    # 使用JavaScript生成user-dun参数
    try:
        with open('../test/同程旅行机票.js', 'r', encoding='utf-8') as f:
            js_file = f.read()
        js_file = js_file.replace("所有的data", json.dumps(data).replace(" ", ''))
        js_file = js_file.replace('当前url', url)
        user_dun = execjs.compile(js_file).call('run')
        print(f"生成的user-dun参数: {user_dun}")
        headers['user-dun'] = user_dun['value']
    except Exception as e:
        print(f"生成user-dun参数失败: {e}")

    data = json.dumps(data, separators=(',', ':'))
    response = requests.post(url, headers=headers, cookies=cookies, data=data)

    # 保存房型关系数据
    with open('酒店房型关系数据.json', 'w', encoding='utf-8') as f:
        try:
            json_data = response.json()
            f.write(json.dumps(json_data, ensure_ascii=False, indent=4))
            print("房型关系数据获取成功并保存")
            return json_data
        except Exception as e:
            print(f"保存房型关系数据失败: {e}")
            f.write(response.text)
            return None

def get_price_plans_for_room(room_id):
    """
    根据房型ID获取价格计划数据
    """
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9",
        "baggage": "sentry-environment=production,sentry-release=EBK-PC-1.139.0,sentry-public_key=8a4553a20fe54c7cbb365f634d8046f0,sentry-trace_id=c0cf655813754302a27905189d4d2c00,sentry-sample_rate=0.1,sentry-transaction=%2Febk-product%2F%3ApathMatch(.*)*,sentry-sampled=false",
        "content-type": "application/json",
        "origin": "https://ebooking.elong.com",
        "priority": "u=1, i",
        "referer": "https://ebooking.elong.com/ebkcommon/dashboard",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "sentry-trace": "c0cf655813754302a27905189d4d2c00-b9342c53d70ceb6e-0",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
        "user-dun": "x77xxxxxIG2Av5txItboHWYTZ1SozxCYhl8dWX7o85pxfFHtvk3hVyzTxNF7uu0wx7xCTcqivhknFAPieSannAZ2ejV67HoNy7xthWsxaiIjStF+Zax8StqNqixj/GF7X8x8xis7Y9Uctho5lJajK4q2ejV67HoNy7xthWsxaiIjStF+Zax8StqNqixj/GF7X8x8xis7Y9Uctho5lJajK4sKckWL7HZmRvQQMi37YBanS0Pxn5auCHPg3lxhCWgp2jinIEPZFzU5vpxxnSancg9tYe2t8EFxeP+ytx4ID3xVKK4BLqxzO89BLeYfp0QgK6Udddtxe5anxHP7n5xR5KwPc/QLECdQJHA4YLj9ZOplywoMoAZO+l2hAa7N+qaQfQ8l81dcQgqoSHBQNbXvMbOT+Rw4CokAoLT4A6rNyrKOanz3oH1+h95oJaBlJI2lAOesQq5KYkiqm157oIah8WLAoIeq0RBvJ9OXgW2MvdsXMapOgMzwhM3TVLcQeQgHydOMOv6lJqpde63Joe3QeQk+zdAQz92+AfslMHnFodV6edVbgHAleQzyg9h6OAT2ngO6QqyId1qApGZ7izIEx7Kxi7=="
    }
    
    cookies = {
        "socketTimePause": "3",
        "socketNextConnectTryTime": "",
        "viewLpsRemind": "null",
        "creditAuditStatus": "null",
        "isShowTaxTips": "null",
        "language": "zh_CN",
        "EbkSessionId": "6c8a74f8c9ba4e2d8790d66c79e792f5",
        "JSESSIONID": "FAAFADF665BC8F642470EC5B9479F7A4",
        "firsttime": "1748366873016",
        "route": "19ef036f19f3784cd71c484acca86aa8",
        "lasttime": "1753264795432"
    }

    url = "https://ebooking.elong.com/product/roomPrice/ajaxRoomRateList"
    data = {
        "hotelId": "26964091",
        "productType": 0,
        "startDate": "2025-07-23",
        "endDate": "2025-08-05",
        "roomTypeId": room_id,  # 添加房型ID参数
        "roomTypeID": room_id   # 尝试不同的参数名
    }

    # 使用JavaScript生成user-dun参数
    try:
        with open('../test/同程旅行机票.js', 'r', encoding='utf-8') as f:
            js_file = f.read()
        js_file = js_file.replace("所有的data", json.dumps(data).replace(" ", ''))
        js_file = js_file.replace('当前url', url)
        user_dun = execjs.compile(js_file).call('run')
        headers['user-dun'] = user_dun['value']
    except Exception as e:
        print(f"生成user-dun参数失败: {e}")

    data = json.dumps(data, separators=(',', ':'))
    response = requests.post(url, headers=headers, cookies=cookies, data=data)

    try:
        response_data = response.json()
        print(f"房型 {room_id} API响应状态码: {response.status_code}")
        print(f"房型 {room_id} API响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)[:500]}...")
        return response_data
    except Exception as e:
        print(f"获取房型 {room_id} 价格计划失败: {e}")
        print(f"房型 {room_id} 原始响应: {response.text[:200]}...")
        return None

def parse_and_generate_relationship():
    """
    解析数据并生成标准化的关系数据
    """
    # 读取房型关系数据
    with open('酒店房型关系数据.json', 'r', encoding='utf-8') as f:
        relationship_data = json.load(f)
    
    # 获取酒店ID
    hotel_id = relationship_data.get('data', {}).get('shotelSupplierInfo', {}).get('mHotelID', '')
    
    # 获取房型信息
    room_response = relationship_data.get('data', {}).get('roomResponse', [])
    
    # 创建结果数据结构
    result = {}
    if hotel_id not in result:
        result[hotel_id] = {}
    
    # 为每个房型分别发送API请求获取价格计划
    for room_info in room_response:
        room_id = room_info.get('roomID', '')
        room_name = room_info.get('roomName', '')
        
        if room_id not in result[hotel_id]:
            result[hotel_id][room_id] = {
                'room_id': room_id,
                'room_name': room_name,
                'room_rateplan_list': {}
            }
        
        print(f"正在为房型 {room_id} ({room_name}) 发送API请求获取价格计划...")
        
        # 为每个房型发送单独的API请求
        price_data = get_price_plans_for_room(room_id)
        if price_data:
            rateplan_index = 0
            # 从rpResponse中获取价格计划数据
            rp_response = price_data.get('data', {}).get('rpResponse', [])
            
            for rate_plan in rp_response:
                if rate_plan.get('roomTypeID') == room_id:
                    rateplan_id = rate_plan.get('ratePlanID', '')
                    rateplan_name = rate_plan.get('cNRatePlanName', '')
                    
                    result[hotel_id][room_id]['room_rateplan_list'][str(rateplan_index)] = {
                        'rateplanid': rateplan_id,
                        'rateplanname': rateplan_name
                    }
                    rateplan_index += 1
            
            print(f"房型 {room_id} 获取到 {rateplan_index} 个价格计划")
        else:
            print(f"房型 {room_id} 获取价格计划失败")
        
        # 添加延迟避免请求过快
        time.sleep(1)
    
    # 保存结果到文件
    output_file = 'parsed_relationship.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=4)
    
    print(f"解析完成，结果已保存到: {output_file}")
    print(f"共解析了 {len(result)} 个酒店的数据")
    
    # 打印示例数据
    if result:
        first_hotel = list(result.keys())[0]
        first_room = list(result[first_hotel].keys())[0]
        print(f"\n示例数据结构:")
        print(f"酒店ID: {first_hotel}")
        print(f"房型ID: {first_room}")
        print(f"房型名称: {result[first_hotel][first_room]['room_name']}")
        print(f"价格计划数量: {len(result[first_hotel][first_room]['room_rateplan_list'])}")
    
    return result

def main():
    """
    主函数：执行完整的获取和解析流程
    """
    print("开始获取酒店房型关系数据...")
    relationship_data = get_room_relationship_data()
    
    if relationship_data:
        print("房型关系数据获取成功，开始解析和生成关系数据...")
        result = parse_and_generate_relationship()
        print("流程完成！")
    else:
        print("房型关系数据获取失败，流程终止")

if __name__ == "__main__":
    main() 