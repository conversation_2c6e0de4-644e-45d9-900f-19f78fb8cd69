import requests
import json
import subprocess  # 执行命令行的
from functools import partial  # 固定某个参数的
subprocess.Popen = partial(subprocess.Popen, encoding='utf-8')
import execjs
import json

headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "content-type": "application/json",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "user-dun": "x77xxxxxIWlnnhtxIipBrxx+CEjszxCYhl8dWX7o85pxEzK050SIi80Mx70ntToyx7xYH6vLborVE+6ZXdPlSorXEFZAer+ErwDgoJNhad46qzjxkwXfqzjxPw4gXHJVJ7ZWFJNUiKwg+Hcots1g6zjeEFZAer+ErwDgoJNhad46qzjxkwXfqzjxPw4gXHJVJ7ZWFJNUiKwg+Hcots1g65SeiVsOVScEFrsdsPoKtwxDt5S5adtDCaC7+dqLQxp7WxPATKQ4Bb18j5f6adPlT6CdRPeyla62HnW42E5ywdUzjE7eUdUu3nUtUFnS3E7rop5K6sAuadtHC5cUiwtC1Xc2jR9sZNtiJO9PzpKFWyXe87iprePKVbscopwVNsbFW1aU9wuSyyZem0xhadYwFBIqwMdyE0bx8b4UY7iNy+K0jE2PunaSR0PphlLyEwxzWb3sQe3eW15UNAxo9+KEM0trhTb7Zw9UNlPcbeqr8XdE4w/u9EGsJ7kKIbf4/JImadX+v0QdoY1EEhdrssFc9vp51cqF86DxEhXeOL/2ilD2zAnu8y/pETUzeIt8aAq8yTz/onSUtDUURah7t7KfIihRaxcta7h9"
}
cookies = {
    "EbkSessionId": "283e10cdaaf342f695142b9d591a0e49",
}
url = "https://ebooking.elong.com/product/roomPrice/ajaxRoomRate"
data = {
    "endDate": "2025-10-20",
    "startDate": "2025-07-22",
    "productType": 0,
    "hotelId": "27105890",
    "roomType": "0002"
}

with open('../test/同程旅行机票.js', 'r', encoding='utf-8') as f:
    js_file = f.read()
js_file = js_file.replace("所有的data", json.dumps(data).replace(" ", ''))
js_file = js_file.replace('当前url', url)
user_dun = execjs.compile(js_file).call('run')
print(user_dun)
headers['user-dun'] = user_dun['value']

data = json.dumps(data, separators=(',', ':'))
response = requests.post(url, data=data,headers=headers, cookies=cookies, )
# 保存数据
with open('../test/同程酒店数据3.json', 'w', encoding='utf-8') as f:
    # 新增功能：将请求返回的数据保存到文件
    # 规范格式：将返回的JSON字符串格式化后写入文件
    try:
        json_data = response.json()
        f.write(json.dumps(json_data, ensure_ascii=False, indent=4))
    except Exception as e:
        # 如果不是JSON格式，直接写入原始内容
        f.write(response.text)
    
