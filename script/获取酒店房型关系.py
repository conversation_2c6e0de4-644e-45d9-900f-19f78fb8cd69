import requests
import json
import subprocess  # 执行命令行的
from functools import partial  # 固定某个参数的
subprocess.Popen = partial(subprocess.Popen, encoding='utf-8')
import execjs
import json

headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "baggage": "sentry-environment=production,sentry-release=EBK-PC-1.139.0,sentry-public_key=8a4553a20fe54c7cbb365f634d8046f0,sentry-trace_id=c0cf655813754302a27905189d4d2c00,sentry-sample_rate=0.1,sentry-transaction=%2Febk-product%2F%3ApathMatch(.*)*,sentry-sampled=false",
    "content-type": "application/json",
    "origin": "https://ebooking.elong.com",
    "priority": "u=1, i",
    "referer": "https://ebooking.elong.com/ebkcommon/dashboard",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sentry-trace": "c0cf655813754302a27905189d4d2c00-b9342c53d70ceb6e-0",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "user-dun": "x77xxxxxIG2Av5txItboHWYTZ1SozxCYhl8dWX7o85pxfFHtvk3hVyzTxNF7uu0wx7xCTcqivhknFAPieSannAZ2ejV67HoNy7xthWsxaiIjStF+Zax8StqNqixj/GF7X8x8xis7Y9Uctho5lJajK4q2ejV67HoNy7xthWsxaiIjStF+Zax8StqNqixj/GF7X8x8xis7Y9Uctho5lJajK4sKckWL7HZmRvQQMi37YBanS0Pxn5auCHPg3lxhCWgp2jinIEPZFzU5vpxxnSancg9tYe2t8EFxeP+ytx4ID3xVKK4BLqxzO89BLeYfp0QgK6Udddtxe5anxHP7n5xR5KwPc/QLECdQJHA4YLj9ZOplywoMoAZO+l2hAa7N+qaQfQ8l81dcQgqoSHBQNbXvMbOT+Rw4CokAoLT4A6rNyrKOanz3oH1+h95oJaBlJI2lAOesQq5KYkiqm157oIah8WLAoIeq0RBvJ9OXgW2MvdsXMapOgMzwhM3TVLcQeQgHydOMOv6lJqpde63Joe3QeQk+zdAQz92+AfslMHnFodV6edVbgHAleQzyg9h6OAT2ngO6QqyId1qApGZ7izIEx7Kxi7=="
}
cookies = {
    "socketTimePause": "3",
    "socketNextConnectTryTime": "",
    "viewLpsRemind": "null",
    "creditAuditStatus": "null",
    "isShowTaxTips": "null",
    "language": "zh_CN",
    "EbkSessionId": "6c8a74f8c9ba4e2d8790d66c79e792f5",
    "JSESSIONID": "FAAFADF665BC8F642470EC5B9479F7A4",
    "firsttime": "1748366873016",
    "route": "19ef036f19f3784cd71c484acca86aa8",
    "lasttime": "1753264795432"
}

url = "https://ebooking.elong.com/product/roomPrice/ajaxRoomRateList"
data = {
    "hotelId": "26964091",
    "productType": 0,
    "startDate": "2025-07-23",
    "endDate": "2025-08-05"
}

# 新增功能：使用JavaScript生成user-dun参数
with open('../test/同程旅行机票.js', 'r', encoding='utf-8') as f:
    js_file = f.read()
js_file = js_file.replace("所有的data", json.dumps(data).replace(" ", ''))
js_file = js_file.replace('当前url', url)
user_dun = execjs.compile(js_file).call('run')
print(user_dun)
headers['user-dun'] = user_dun['value']

data = json.dumps(data, separators=(',', ':'))
response = requests.post(url, headers=headers, cookies=cookies, data=data)

# 新增功能：保存数据到文件
with open('酒店房型关系数据.json', 'w', encoding='utf-8') as f:
    # 新增功能：将请求返回的数据保存到文件
    # 规范格式：将返回的JSON字符串格式化后写入文件
    try:
        json_data = response.json()
        f.write(json.dumps(json_data, ensure_ascii=False, indent=4))
    except Exception as e:
        # 如果不是JSON格式，直接写入原始内容
        f.write(response.text)

print(response.text)
print(response)