import time


class BrowserConfig:
    headless = False

    import os
    # 获取当前项目目录并拼接资源文件路径
    project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    cookies_path = os.path.join(project_dir, "resource", "cookies.json")
    encrypt_js_path = os.path.join(project_dir, "resource", "tongcheng.js")



    class send_hotel_request:
        headers = {
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9",
            "content-type": "application/json",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "user-dun": "x77xxxxxIWlnnhtxIipBrxx+CEjszxCYhl8dWX7o85pxEzK050SIi80Mx70ntToyx7xYH6vLborVE+6ZXdPlSorXEFZAer+ErwDgoJNhad46qzjxkwXfqzjxPw4gXHJVJ7ZWFJNUiKwg+Hcots1g6zjeEFZAer+ErwDgoJNhad46qzjxkwXfqzjxPw4gXHJVJ7ZWFJNUiKwg+Hcots1g65SeiVsOVScEFrsdsPoKtwxDt5S5adtDCaC7+dqLQxp7WxPATKQ4Bb18j5f6adPlT6CdRPeyla62HnW42E5ywdUzjE7eUdUu3nUtUFnS3E7rop5K6sAuadtHC5cUiwtC1Xc2jR9sZNtiJO9PzpKFWyXe87iprePKVbscopwVNsbFW1aU9wuSyyZem0xhadYwFBIqwMdyE0bx8b4UY7iNy+K0jE2PunaSR0PphlLyEwxzWb3sQe3eW15UNAxo9+KEM0trhTb7Zw9UNlPcbeqr8XdE4w/u9EGsJ7kKIbf4/JImadX+v0QdoY1EEhdrssFc9vp51cqF86DxEhXeOL/2ilD2zAnu8y/pETUzeIt8aAq8yTz/onSUtDUURah7t7KfIihRaxcta7h9"
        }
        cookies = {"EbkSessionId": "283e10cdaaf342f695142b9d591a0e49"}


        today = time.strftime("%Y-%m-%d")
        days_90_later = time.strftime(
            "%Y-%m-%d",
            time.localtime(time.time() + 90 * 24 * 60 * 60)
        )
        data = {
            "startDate": today,
            "endDate": days_90_later,
            "productType": 0,
            "hotelId": "27105890",
            "roomType": "0002",
        }

