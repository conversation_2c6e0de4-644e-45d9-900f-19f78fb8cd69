import json
from tongcheng.hotel_data.hotel_operator import TongchengHotelOperator
from tongcheng.config.browser_config import BrowserConfig
from DrissionPage import ChromiumPage,ChromiumOptions
from tongcheng.hotel_data.send_hotel_request import TongchengHotelRequestSender
import time
import logging
from tongcheng.hotel_data.get_roomid import RoomRelationshipGetter
class TongchengHotelDataGetter(TongchengHotelOperator):
    def __init__(self, logger, page):

        super().__init__(page, logger)
        self.logger = logger  # 补全logger初始化
        self.co = ChromiumOptions()
        self.co.headless(BrowserConfig.headless)
        self.hotel_operator = None
        self.base_url = "https://ebooking.elong.com"
        self.dashboard_url = "https://ebooking.elong.com/ebkcommon/dashboard#/dashboard"
        self.cookies_path = BrowserConfig.cookies_path

    def connect(self):
        print("connecting...")
        self.page = ChromiumPage(self.co)
        with open(self.cookies_path, "r") as f:
            cookies = json.load(f)
        self.page.get(self.base_url)
        self.page.set.cookies(cookies)
        self.page.get(self.dashboard_url)
        self.hotel_operator = TongchengHotelOperator(self.page, self.logger)
        self.hotel_request = TongchengHotelRequestSender(logger=self.logger)
        self.room_relationship_getter = RoomRelationshipGetter(logger=self.logger)

    def get_hotel_data(self, hotel_name: str,days: int):
        # 重置累积数据，开始新的酒店数据获取
        self.reset_accumulated_data()
        
        self.hotel_operator.find_and_enter_hotel(hotel_name)
        self.hotel_operator.click_price_maintain()
        start_date = time.strftime("%Y-%m-%d")
        end_date = time.strftime(
            "%Y-%m-%d",
            time.localtime(time.time() + days * 24 * 60 * 60)
        )
        
        # 使用固定的酒店ID，因为get_room_list方法需要hotel_id参数
        hotel_id = "26964091"
        room_list, room_name_map = self.room_relationship_getter.get_room_list(hotel_id, start_date, end_date)
        

        
        for room in room_list:
            print(f"正在获取房型 {room} 的数据...")
            response_data = self.hotel_request.get_hotel_price(hotel_id=hotel_id, room_type=room,
                                           start_date=start_date, end_date=end_date,
                                           save_to_file=True)
            self.parse_room_list_from_response(response_data, room_name_map)

    def parse_room_list_from_response(self, response_data, room_name_map):
        """
        解析响应数据中的房型信息和价格计划信息
        参考parse_relationship.py的逻辑生成房型关系数据
        
        Args:
            response_data: 酒店价格查询的响应数据
            :param response_data:
            :param room_name_map:
        """
        try:
            self.logger.info("开始解析房型列表响应数据...")
            

            
            # 从响应数据中获取房型信息
            data = response_data.get('data', {})
            list_rate_plan = data.get('listRatePlan', [])
            
            # 获取酒店ID
            hotel_id = None
            if list_rate_plan:
                hotel_id = list_rate_plan[0].get('hotelID', '')
            
            if not hotel_id:
                self.logger.warning("未找到酒店ID，无法解析房型数据")
                return
            
            # 初始化累积结果数据结构（如果不存在）
            if not hasattr(self, 'accumulated_result'):
                self.accumulated_result = {}
            
            # 确保酒店ID存在于累积结果中
            if hotel_id not in self.accumulated_result:
                self.accumulated_result[hotel_id] = {}
            
            # 按房型ID组织数据
            room_rateplan_map = {}
            
            # 遍历价格计划，按房型分组
            for rate_plan in list_rate_plan:
                room_id = rate_plan.get('roomTypeID', '')
                rateplan_id = rate_plan.get('ratePlanID', '')
                rateplan_name = rate_plan.get('cNRatePlanName', '')
                
                if room_id not in room_rateplan_map:
                    room_rateplan_map[room_id] = []
                
                room_rateplan_map[room_id].append({
                    'rateplanid': rateplan_id,
                    'rateplanname': rateplan_name
                })
            
            # 为每个房型创建或更新数据结构
            for room_id, rateplans in room_rateplan_map.items():
                if room_id not in self.accumulated_result[hotel_id]:
                    # 获取真实的房型名称，如果不存在则使用默认名称
                    room_name = room_name_map.get(room_id, f"房型{room_id}")
                    
                    self.accumulated_result[hotel_id][room_id] = {
                        'room_id': room_id,
                        'room_name': room_name,
                        'room_rateplan_list': {}
                    }
                
                # 添加价格计划信息
                for index, rateplan in enumerate(rateplans):
                    self.accumulated_result[hotel_id][room_id]['room_rateplan_list'][str(index)] = {
                        'rateplanid': rateplan['rateplanid'],
                        'rateplanname': rateplan['rateplanname']
                    }
            
            # 保存累积结果到文件
            output_file = 'parsed_room_relationship.json'
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.accumulated_result, f, ensure_ascii=False, indent=4)
            
            self.logger.info(f"解析完成，结果已保存到: {output_file}")
            self.logger.info(f"当前累积了 {len(self.accumulated_result)} 个酒店的数据")
            
            # 打印示例数据
            if self.accumulated_result:
                first_hotel = list(self.accumulated_result.keys())[0]
                first_room = list(self.accumulated_result[first_hotel].keys())[0]
                self.logger.info(f"示例数据结构:")
                self.logger.info(f"酒店ID: {first_hotel}")
                self.logger.info(f"房型ID: {first_room}")
                self.logger.info(f"房型名称: {self.accumulated_result[first_hotel][first_room]['room_name']}")
                self.logger.info(f"价格计划数量: {len(self.accumulated_result[first_hotel][first_room]['room_rateplan_list'])}")
            
            return self.accumulated_result
            
        except Exception as e:
            self.logger.error(f"解析房型列表响应数据失败: {str(e)}")
            raise
    
    def reset_accumulated_data(self):
        """
        重置累积的数据，开始新的酒店数据获取
        """
        self.accumulated_result = {}
        self.logger.info("已重置累积数据，开始新的酒店数据获取")


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    hotel_getter = TongchengHotelDataGetter(logger,page=None)
    hotel_name = '新加坡费尔蒙酒店'
    hotel_getter.connect()
    hotel_getter.get_hotel_data(hotel_name, 14)