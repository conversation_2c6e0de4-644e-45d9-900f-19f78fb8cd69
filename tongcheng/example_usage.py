#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同程酒店请求发送器使用示例
演示如何使用TongchengHotelRequestSender类发送酒店价格查询请求
"""

import logging
from tongcheng.hotel_data.send_hotel_request import TongchengHotelRequestSender

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('hotel_request.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

def example_basic_usage():
    """基本使用示例"""
    logger = setup_logging()
    logger.info("开始基本使用示例")
    
    # 创建请求发送器实例
    sender = TongchengHotelRequestSender(logger=logger)
    
    # 设置请求参数
    hotel_id = "27105890"
    room_type = "0002"
    start_date = "2025-07-22"
    end_date = "2025-10-20"
    
    try:
        # 发送价格查询请求
        response_data = sender.get_hotel_price(
            hotel_id=hotel_id,
            room_type=room_type,
            start_date=start_date,
            end_date=end_date,
            save_to_file=True,
            file_path="酒店价格数据.json"
        )
        
        logger.info("价格查询请求成功")
        logger.info(f"响应数据包含 {len(response_data)} 个字段")
        
        # 打印响应数据的关键信息
        if 'success' in response_data:
            logger.info(f"请求状态: {response_data['success']}")
        
        return response_data
        
    except Exception as e:
        logger.error(f"请求失败: {str(e)}")
        raise

def example_custom_parameters():
    """自定义参数示例"""
    logger = setup_logging()
    logger.info("开始自定义参数示例")
    
    sender = TongchengHotelRequestSender(logger=logger)
    
    # 自定义请求头
    custom_headers = {
        "X-Custom-Header": "custom-value",
        "Authorization": "Bearer your-token-here"
    }
    
    # 自定义cookies
    custom_cookies = {
        "sessionId": "your-session-id",
        "userId": "your-user-id"
    }
    
    try:
        # 使用自定义参数发送请求
        response_data = sender.send_price_request(
            hotel_id="27105890",
            room_type="0001",  # 不同的房型
            start_date="2025-08-01",
            end_date="2025-08-15",
            product_type=1,
            custom_headers=custom_headers,
            custom_cookies=custom_cookies
        )
        
        logger.info("自定义参数请求成功")
        return response_data
        
    except Exception as e:
        logger.error(f"自定义参数请求失败: {str(e)}")
        raise

def example_batch_requests():
    """批量请求示例"""
    logger = setup_logging()
    logger.info("开始批量请求示例")
    
    sender = TongchengHotelRequestSender(logger=logger)
    
    # 多个酒店ID
    hotel_ids = ["27105890", "27105891", "27105892"]
    room_types = ["0001", "0002", "0003"]
    
    results = []
    
    for hotel_id in hotel_ids:
        for room_type in room_types:
            try:
                logger.info(f"查询酒店 {hotel_id} 房型 {room_type}")
                
                response_data = sender.send_price_request(
                    hotel_id=hotel_id,
                    room_type=room_type,
                    start_date="2025-07-22",
                    end_date="2025-07-25"
                )
                
                results.append({
                    'hotel_id': hotel_id,
                    'room_type': room_type,
                    'data': response_data
                })
                
                logger.info(f"酒店 {hotel_id} 房型 {room_type} 查询成功")
                
            except Exception as e:
                logger.error(f"酒店 {hotel_id} 房型 {room_type} 查询失败: {str(e)}")
                continue
    
    logger.info(f"批量查询完成，成功查询 {len(results)} 个组合")
    return results

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始运行同程酒店请求发送器示例")
    
    try:
        # 示例1：基本使用
        logger.info("=" * 50)
        logger.info("示例1：基本使用")
        example_basic_usage()
        
        # 示例2：自定义参数
        logger.info("=" * 50)
        logger.info("示例2：自定义参数")
        example_custom_parameters()
        
        # 示例3：批量请求
        logger.info("=" * 50)
        logger.info("示例3：批量请求")
        example_batch_requests()
        
        logger.info("所有示例运行完成")
        
    except Exception as e:
        logger.error(f"示例运行失败: {str(e)}")
        raise

if __name__ == "__main__":
    main() 