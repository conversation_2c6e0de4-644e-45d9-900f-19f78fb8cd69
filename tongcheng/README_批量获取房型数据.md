# 同程酒店房型关系数据批量获取器

## 功能说明

这个脚本使用浏览器自动化方式，批量获取同程酒店所有房型的价格计划数据，并生成标准化的关系数据结构。

## 文件说明

- `get_all_room_relationship_simple.py`: 主要的批量获取脚本
- `parsed_relationship.json`: 最终生成的关系数据文件

## 使用方法

### 1. 环境准备

确保已安装必要的依赖：
```bash
pip install DrissionPage requests
```

### 2. 登录准备

在运行脚本前，需要先手动登录同程酒店系统，并保存cookies到 `tongcheng/resource/cookies.json` 文件中。

### 3. 运行脚本

```bash
cd tongcheng
python get_all_room_relationship_simple.py
```

## 工作流程

1. **连接浏览器**: 启动Chrome浏览器并加载cookies
2. **进入酒店页面**: 自动搜索并进入指定酒店的价格维护页面
3. **循环获取房型数据**: 
   - 房型0011: 豪华大床房 - 带阳台
   - 房型0012: 豪华双床房（阳台）
   - 房型0013: 豪华大床房（港景，带阳台）
   - 房型0014: 招牌大床套房 - 带两个阳台
4. **解析数据**: 从API响应中提取价格计划信息
5. **生成结果**: 保存为 `parsed_relationship.json` 文件

## 输出格式

生成的 `parsed_relationship.json` 文件格式如下：

```json
{
    "61501732": {
        "0013": {
            "room_id": "0013",
            "room_name": "豪华大床房（港景，带阳台）",
            "room_rateplan_list": {
                "0": {
                    "rateplanid": 439184892,
                    "rateplanname": "含双早1-1111"
                },
                "1": {
                    "rateplanid": 439184941,
                    "rateplanname": "含双早2-2222"
                }
            }
        },
        "0011": {
            "room_id": "0011",
            "room_name": "豪华大床房 - 带阳台",
            "room_rateplan_list": {
                "0": {
                    "rateplanid": 439182121,
                    "rateplanname": "含双早1-11"
                }
            }
        }
    }
}
```

## 配置说明

### 房型列表
在脚本中可以修改 `room_list` 变量来调整需要获取的房型：

```python
self.room_list = ['0011', '0012', '0013', '0014']
```

### 酒店信息
可以修改 `main()` 函数中的酒店名称：

```python
result = getter.get_all_room_data("新加坡费尔蒙酒店", days=14)
```

### 查询天数
可以调整查询的天数范围：

```python
result = getter.get_all_room_data("新加坡费尔蒙酒店", days=14)  # 14天
```

## 注意事项

1. **浏览器模式**: 默认使用有头模式（`headless=False`），可以在 `browser_config.py` 中修改
2. **延迟设置**: 脚本中设置了适当的延迟来避免请求过快
3. **错误处理**: 即使某个房型获取失败，也会创建空的结构
4. **数据覆盖**: 每次运行都会覆盖之前的 `同程酒店数据.json` 文件

## 故障排除

### 常见问题

1. **cookies加载失败**
   - 确保已手动登录并保存了cookies
   - 检查cookies文件路径是否正确

2. **浏览器启动失败**
   - 确保Chrome浏览器已安装
   - 检查DrissionPage配置

3. **数据获取失败**
   - 检查网络连接
   - 确认登录状态是否有效
   - 增加延迟时间

### 调试模式

可以设置 `headless=False` 来查看浏览器操作过程：

```python
# 在 browser_config.py 中
headless = False
```

## 扩展功能

如需添加更多房型或修改数据结构，可以：

1. 修改 `room_list` 添加新房型
2. 在 `_get_room_info()` 中添加新房型名称
3. 调整输出格式以满足特定需求 