import json
import time
import logging
import sys
import os
import requests
from DrissionPage import ChromiumPage, ChromiumOptions

# 添加父目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TongchengAllRoomRelationshipGetter:
    def __init__(self):
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        self.room_list = []  # 动态获取
        self.room_info_map = {}  # room_id -> room_name
        self.result = {}
        
        # 初始化浏览器配置
        self.co = ChromiumOptions()
        self.co.headless(False)  # 使用有头模式，方便调试
        self.page = None
        
        # 参考获取酒店房型关系.py的请求配置
        self.headers = {
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9",
            "baggage": "sentry-environment=production,sentry-release=EBK-PC-1.139.0,sentry-public_key=8a4553a20fe54c7cbb365f634d8046f0,sentry-trace_id=c0cf655813754302a27905189d4d2c00,sentry-sample_rate=0.1,sentry-transaction=%2Febk-product%2F%3ApathMatch(.*)*,sentry-sampled=false",
            "content-type": "application/json",
            "origin": "https://ebooking.elong.com",
            "priority": "u=1, i",
            "referer": "https://ebooking.elong.com/ebkcommon/dashboard",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "sentry-trace": "c0cf655813754302a27905189d4d2c00-b9342c53d70ceb6e-0",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        }
        
        # 参考获取酒店房型关系.py的cookies配置
        self.cookies = {
            "socketTimePause": "3",
            "socketNextConnectTryTime": "",
            "viewLpsRemind": "null",
            "creditAuditStatus": "null",
            "isShowTaxTips": "null",
            "language": "zh_CN",
            "EbkSessionId": "6c8a74f8c9ba4e2d8790d66c79e792f5",
            "JSESSIONID": "FAAFADF665BC8F642470EC5B9479F7A4",
            "firsttime": "1748366873016",
            "route": "19ef036f19f3784cd71c484acca86aa8",
            "lasttime": "1753264795432"
        }
        
        # 房型列表获取接口
        self.room_list_api_url = "https://ebooking.elong.com/product/roomPrice/ajaxRoomRateList"
        # 房型价格获取接口
        self.price_api_url = "https://ebooking.elong.com/product/roomPrice/ajaxRoomRate"

    def connect_browser(self):
        """连接浏览器并登录"""
        try:
            print("正在连接浏览器...")
            self.page = ChromiumPage(self.co)
            
            # 加载cookies
            try:
                with open('tongcheng/resource/cookies.json', "r") as f:
                    cookies = json.load(f)
                self.page.get("https://ebooking.elong.com")
                self.page.set.cookies(cookies)
                self.page.get("https://ebooking.elong.com/ebkcommon/dashboard#/dashboard")
                print("浏览器连接成功，已加载cookies")
                return True
            except Exception as e:
                print(f"加载cookies失败: {e}")
                print("请确保已登录并保存了cookies")
                return False
        except Exception as e:
            print(f"浏览器连接失败: {e}")
            return False

    def find_and_enter_hotel(self, hotel_name: str) -> bool:
        """查找并进入指定酒店页面"""
        try:
            self.logger.info(f"正在查找酒店: {hotel_name}")
            hotel_selector = self.page.ele('x://*[@id="common_app"]/div/div[2]/div/div[2]/span[1]/span/div/span')
            if hotel_selector:
                hotel_selector.click()
                time.sleep(0.8)
            else:
                self.logger.error("未找到酒店选择器")
                return False
            input_selector = 'x://*[@id="common_app"]/div/div[4]/div/div[2]/div/div[1]/div[5]/div/input'
            input_line = self.page.ele(input_selector)
            if input_line:
                input_line.click()
                input_line.input(hotel_name)
                time.sleep(0.8)
            else:
                self.logger.error("未找到酒店输入框")
                return False
            option_selector = '.el-tooltip option-text item'
            option = self.page.ele(option_selector)
            if option:
                option.click()
                time.sleep(0.8)
            else:
                self.logger.error("未找到酒店备选项")
                return False
            search_selector = 'x://*[@id="common_app"]/div/div[4]/div/div[2]/div/div[1]/button'
            search_button = self.page.ele(search_selector)
            if search_button:
                search_button.click()
                time.sleep(1)
            else:
                self.logger.error("未找到查询按钮")
                return False
            hotel_result_selector = '.w-1/3 pt-1.0 pb-1.0 text-14 cursor-pointer'
            hotel_result = self.page.ele(hotel_result_selector)
            if hotel_result:
                hotel_result.click()
                time.sleep(2)
                self.logger.info(f"成功进入酒店: {hotel_name}")
                return True
            else:
                self.logger.error("未找到查询结果中的酒店")
                return False
        except Exception as e:
            self.logger.error(f"查找酒店失败: {str(e)}")
            return False

    def click_price_maintain(self) -> bool:
        """点击房价维护按钮"""
        try:
            # 等待页面加载完成
            time.sleep(1)
            btn = self.page.ele('x://*[@id="common_app"]/div/div[3]/div[1]/div/div/div[2]/div[1]/div/ul[4]/li')
            if btn:
                btn.click()
                time.sleep(2)  # 等待页面响应
                self.logger.info("房价维护按钮点击成功")
                return True
            else:
                self.logger.error("未找到房价维护按钮")
                return False
        except Exception as e:
            self.logger.error(f"点击房价维护按钮异常: {str(e)}")
            return False

    def get_room_list(self, hotel_id: str, start_date: str, end_date: str):
        """获取酒店的所有房型列表"""
        try:
            # 构建获取房型列表的请求数据
            data = {
                "hotelId": hotel_id,
                "productType": 0,
                "startDate": start_date,
                "endDate": end_date
            }
            
            # 发送请求获取房型列表
            response = requests.post(
                self.room_list_api_url,
                json=data,
                headers=self.headers,
                cookies=self.cookies,
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                # 保存房型列表响应数据
                with open('酒店房型关系数据.json', 'w', encoding='utf-8') as f:
                    json.dump(response_data, f, ensure_ascii=False, indent=4)
                self.logger.info(f"房型列表请求成功，响应状态码: {response.status_code}")
                return response_data
            else:
                self.logger.error(f"房型列表请求失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"发送房型列表请求异常: {str(e)}")
            return None

    def parse_room_list_from_response(self, response_data):
        """从房型列表响应中解析房型信息"""
        room_list = []
        room_info_map = {}
        
        try:
            # 从响应中的roomMap获取房型ID列表
            room_map = response_data.get('data', {}).get('roomMap', {})
            room_response = response_data.get('data', {}).get('roomResponse', [])
            
            # 从roomMap中获取房型ID
            for room_id in room_map.keys():
                if room_id not in room_list:
                    room_list.append(room_id)
                    # 从roomResponse中获取房型名称
                    room_name = f'房型{room_id}'  # 默认名称
                    for room_info in room_response:
                        if room_info.get('roomID') == room_id:
                            room_name = room_info.get('roomName', f'房型{room_id}')
                            break
                    room_info_map[room_id] = room_name
                    print(f"发现房型: {room_id} - {room_name}")
            
            # 如果没有从roomMap中找到房型，使用备用方案
            if not room_list:
                print("从roomMap中未找到房型信息，使用备用房型列表...")
                backup_room_list = ['0011', '0012', '0013', '0014']
                for room_id in backup_room_list:
                    room_list.append(room_id)
                    room_info_map[room_id] = f'房型{room_id}'
                    print(f"使用备用房型: {room_id}")
                    
        except Exception as e:
            print(f"解析房型列表失败: {e}")
            # 使用备用房型列表
            backup_room_list = ['0011', '0012', '0013', '0014']
            for room_id in backup_room_list:
                room_list.append(room_id)
                room_info_map[room_id] = f'房型{room_id}'
                print(f"使用备用房型: {room_id}")
        
        return room_list, room_info_map

    def send_price_request(self, hotel_id: str, room_type: str, start_date: str, end_date: str):
        """发送价格查询请求"""
        try:
            # 构建价格查询请求数据
            data = {
                "hotelID": hotel_id,
                "roomTypeID": room_type,
                "startDate": start_date,
                "endDate": end_date,
                "productType": 0
            }
            
            # 发送请求
            response = requests.post(
                self.price_api_url,
                json=data,
                headers=self.headers,
                cookies=self.cookies,
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                # 保存响应数据
                with open('同程酒店数据.json', 'w', encoding='utf-8') as f:
                    json.dump(response_data, f, ensure_ascii=False, indent=4)
                self.logger.info(f"房型 {room_type} 请求成功，响应状态码: {response.status_code}")
                return response_data
            else:
                self.logger.error(f"房型 {room_type} 请求失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"发送房型 {room_type} 请求异常: {str(e)}")
            return None

    def get_all_room_data(self, hotel_name: str, days: int = 14):
        """获取酒店的所有房型数据"""
        print(f"开始获取酒店 '{hotel_name}' 的所有房型数据...")
        hotel_id = "26964091"  # 新加坡费尔蒙酒店的ID
        self.result[hotel_id] = {}

        start_date = time.strftime("%Y-%m-%d")
        end_date = time.strftime(
            "%Y-%m-%d",
            time.localtime(time.time() + days * 24 * 60 * 60)
        )
        
        # 第一步：浏览器登录和点击房价接口
        print("第一步：浏览器登录和点击房价接口...")
        if not self.connect_browser():
            print("浏览器连接失败，程序退出")
            return self.result
        
        if not self.find_and_enter_hotel(hotel_name):
            print("进入酒店页面失败，程序退出")
            return self.result
        
        if not self.click_price_maintain():
            print("点击房价维护按钮失败，程序退出")
            return self.result
        
        print("浏览器操作完成，开始获取房型列表...")
        
        # 第二步：获取房型列表
        print("第二步：获取房型列表...")
        room_list_response = self.get_room_list(hotel_id, start_date, end_date)
        if room_list_response:
            print("房型列表获取成功")
            # 解析房型列表
            self.room_list, self.room_info_map = self.parse_room_list_from_response(room_list_response)
        else:
            print("房型列表获取失败，使用备用房型列表")
            # 使用备用房型列表
            self.room_list = ['0011', '0012', '0013', '0014']
            self.room_info_map = {room_id: f'房型{room_id}' for room_id in self.room_list}
        
        print(f"获取到 {len(self.room_list)} 个房型: {self.room_list}")
        
        # 第三步：根据房型列表发送多次请求获取实际数据
        print(f"\n第三步：开始发送 {len(self.room_list)} 次请求获取房型数据...")
        
        for i, room_type in enumerate(self.room_list, 1):
            print(f"\n[{i}/{len(self.room_list)}] 正在获取房型 {room_type} 的数据...")
            try:
                # 发送价格查询请求
                response = self.send_price_request(
                    hotel_id=hotel_id,
                    room_type=room_type,
                    start_date=start_date,
                    end_date=end_date
                )
                if response:
                    print(f"房型 {room_type} 请求发送成功")
                    time.sleep(3)  # 等待响应处理
                    self._parse_room_data_from_response(room_type, hotel_id)
                    print(f"房型 {room_type} 数据解析完成")
                else:
                    print(f"房型 {room_type} 请求失败")
                    self._create_empty_room_structure(room_type, hotel_id)
            except Exception as e:
                print(f"获取房型 {room_type} 数据失败: {e}")
                self._create_empty_room_structure(room_type, hotel_id)
            time.sleep(2)  # 请求间隔
        
        self._save_result()
        print(f"\n所有房型数据获取完成！")
        print(f"共处理了 {len(self.room_list)} 个房型")
        return self.result

    def _parse_room_data_from_response(self, room_type: str, hotel_id: str):
        """从浏览器获取的接口响应中解析房型数据"""
        try:
            # 从最新的接口响应中获取价格计划数据
            with open('同程酒店数据.json', 'r', encoding='utf-8') as f:
                hotel_data = json.load(f)
            
            room_name = self.room_info_map.get(room_type, f'房型{room_type}')
            if room_type not in self.result[hotel_id]:
                self.result[hotel_id][room_type] = {
                    'room_id': room_type,
                    'room_name': room_name,
                    'room_rateplan_list': {}
                }
            
            # 从接口响应中解析价格计划
            rateplan_index = 0
            list_rate_plan = hotel_data.get('data', {}).get('listRatePlan', [])
            for rate_plan in list_rate_plan:
                if rate_plan.get('roomTypeID') == room_type:
                    rateplan_id = rate_plan.get('ratePlanID', '')
                    rateplan_name = rate_plan.get('cNRatePlanName', '')
                    self.result[hotel_id][room_type]['room_rateplan_list'][str(rateplan_index)] = {
                        'rateplanid': rateplan_id,
                        'rateplanname': rateplan_name
                    }
                    rateplan_index += 1
            print(f"房型 {room_type} 解析到 {rateplan_index} 个价格计划")
        except Exception as e:
            print(f"解析房型 {room_type} 数据失败: {e}")
            self._create_empty_room_structure(room_type, hotel_id)

    def _create_empty_room_structure(self, room_type: str, hotel_id: str):
        """创建空的房型结构"""
        room_name = self.room_info_map.get(room_type, f'房型{room_type}')
        if room_type not in self.result[hotel_id]:
            self.result[hotel_id][room_type] = {
                'room_id': room_type,
                'room_name': room_name,
                'room_rateplan_list': {}
            }

    def _save_result(self):
        """保存结果到文件"""
        output_file = 'parsed_relationship.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.result, f, ensure_ascii=False, indent=4)
        print(f"结果已保存到: {output_file}")
        
        # 修复：添加空结果检查，避免列表索引越界
        if self.result:
            first_hotel = list(self.result.keys())[0]
            if self.result[first_hotel]:  # 检查酒店是否有房型数据
                first_room = list(self.result[first_hotel].keys())[0]
                print(f"\n示例数据结构:")
                print(f"酒店ID: {first_hotel}")
                print(f"房型ID: {first_room}")
                print(f"房型名称: {self.result[first_hotel][first_room]['room_name']}")
                print(f"价格计划数量: {len(self.result[first_hotel][first_room]['room_rateplan_list'])}")
            else:
                print(f"\n酒店 {first_hotel} 没有房型数据")
        else:
            print("\n没有获取到任何数据")

def main():
    print("=== 同程酒店房型关系数据获取器 ===")
    getter = TongchengAllRoomRelationshipGetter()
    try:
        result = getter.get_all_room_data("新加坡费尔蒙酒店", days=14)
        print("\n=== 获取完成 ===")
        print(f"共获取了 {len(result)} 个酒店的数据")
    except Exception as e:
        print(f"程序执行失败: {e}")
    finally:
        # 关闭浏览器
        if getter.page:
            getter.page.quit()
            print("浏览器已关闭")

if __name__ == "__main__":
    main() 