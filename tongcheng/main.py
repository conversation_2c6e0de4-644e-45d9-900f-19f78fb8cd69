import logging
from tongcheng.hotel_data.get_hotel_data import TongchengHotelDataGetter

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """
    主函数：调用同程酒店数据获取器和请求发送器
    """
    # 创建酒店数据获取器实例
    # 注意：这里page参数为None，因为会在connect方法中创建
    hotel_getter = TongchengHotelDataGetter(logger=logger, page=None)
    hotel_name = '新加坡费尔蒙酒店'
    hotel_getter.connect()
    hotel_getter.get_hotel_data(hotel_name, 14)



if __name__ == "__main__":
    main()
