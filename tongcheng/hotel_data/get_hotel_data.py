import json
from tongcheng.hotel_data.hotel_operator import TongchengHotelOperator
from tongcheng.config.browser_config import BrowserConfig
from DrissionPage import ChromiumPage,ChromiumOptions
from tongcheng.hotel_data.send_hotel_request import TongchengHotelRequestSender
import time

class TongchengHotelDataGetter(TongchengHotelOperator):
    def __init__(self, logger, page):

        super().__init__(page, logger)
        self.logger = logger  # 补全logger初始化
        self.co = ChromiumOptions()
        self.co.headless(BrowserConfig.headless)
        self.hotel_operator = None
        self.base_url = "https://ebooking.elong.com"
        self.dashboard_url = "https://ebooking.elong.com/ebkcommon/dashboard#/dashboard"
        self.cookies_path = BrowserConfig.cookies_path

    def connect(self):
        print("connecting...")
        self.page = ChromiumPage(self.co)
        with open(self.cookies_path, "r") as f:
            cookies = json.load(f)
        self.page.get(self.base_url)
        self.page.set.cookies(cookies)
        self.page.get(self.dashboard_url)
        self.hotel_operator = TongchengHotelOperator(self.page, self.logger)
        self.hotel_request = TongchengHotelRequestSender(logger=self.logger)

    def get_hotel_data(self, hotel_name: str,days: int):
        self.hotel_operator.find_and_enter_hotel(hotel_name)
        self.hotel_operator.click_price_maintain()
        start_date = time.strftime("%Y-%m-%d")
        end_date = time.strftime(
            "%Y-%m-%d",
            time.localtime(time.time() + days * 24 * 60 * 60)
        )

        self.hotel_request.get_hotel_price(hotel_id="26964091", room_type="0014",
                                           start_date=start_date, end_date=end_date,
                                           save_to_file=True)





