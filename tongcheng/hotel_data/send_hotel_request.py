import requests
import json
import subprocess
from functools import partial
import logging
from typing import Dict, Any, Optional
from tongcheng.config.browser_config import BrowserConfig


# 设置subprocess编码
subprocess.Popen = partial(subprocess.Popen, encoding='utf-8')
import execjs


class TongchengHotelRequestSender:
    """
    同程酒店请求发送器
    负责发送酒店相关的HTTP请求，包括价格查询等
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化酒店请求发送器
        
        Args:
            logger: 日志记录器，如果为None则使用默认配置
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 默认请求头
        self.default_headers = BrowserConfig.send_hotel_request.headers
        
        # 默认cookies
        self.default_cookies = BrowserConfig.send_hotel_request.cookies
        
        # API端点
        self.price_api_url = "https://ebooking.elong.com/product/roomPrice/ajaxRoomRate"
        
        # JS文件路径
        self.js_file_path = BrowserConfig.encrypt_js_path
        
    def _load_js_file(self) -> str:
        """
        加载JS加密文件
        
        Returns:
            str: JS文件内容
        """
        try:
            with open(self.js_file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            self.logger.error(f"JS文件未找到: {self.js_file_path}")
            raise
        except Exception as e:
            self.logger.error(f"读取JS文件失败: {str(e)}")
            raise
    
    def _generate_user_dun(self, data: Dict[str, Any], url: str) -> str:
        """
        生成user-dun加密值
        
        Args:
            data: 请求数据
            url: 请求URL
            
        Returns:
            str: 加密后的user-dun值
        """
        try:
            js_file = self._load_js_file()
            
            # 替换JS文件中的占位符
            js_file = js_file.replace("所有的data", json.dumps(data).replace(" ", ''))
            js_file = js_file.replace('当前url', url)
            
            # 设置环境变量以解决Windows编码问题
            import os
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            
            # 执行JS代码
            user_dun = execjs.compile(js_file).call('run')
            self.logger.info(f"成功生成user-dun: {user_dun}")
            
            return user_dun['value']
            
        except Exception as e:
            self.logger.error(f"生成user-dun失败: {str(e)}")
            raise


    def send_room_type_request(self):
        pass

    def send_price_request(self,
                          hotel_id: str,
                          room_type: str ,
                          start_date: str,
                          end_date: str,
                          product_type: int = 0,
                          custom_headers: Optional[Dict[str, str]] = None,
                          custom_cookies: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        发送价格查询请求
        
        Args:
            hotel_id: 酒店ID
            room_type: 房型代码
            start_date: 开始日期
            end_date: 结束日期
            product_type: 产品类型
            custom_headers: 自定义请求头
            custom_cookies: 自定义cookies
            
        Returns:
            Dict[str, Any]: 响应数据
        """
        try:
            # 构建请求数据
            request_data = {
                "endDate": end_date,
                "startDate": start_date,
                "productType": product_type,
                "hotelId": hotel_id,
                "roomType": room_type
            }
            
            self.logger.info(f"发送价格查询请求，酒店ID: {hotel_id}, 房型: {room_type}")
            
            # 生成user-dun
            user_dun = self._generate_user_dun(request_data, self.price_api_url)
            
            # 构建请求头
            headers = self.default_headers.copy()
            headers['user-dun'] = user_dun
            
            # 合并自定义请求头
            if custom_headers:
                headers.update(custom_headers)
            
            # 构建cookies
            cookies = self.default_cookies.copy()
            if custom_cookies:
                cookies.update(custom_cookies)
            
            # 发送请求
            data = json.dumps(request_data, separators=(',', ':'))
            response = requests.post(
                self.price_api_url, 
                data=data,
                headers=headers, 
                cookies=cookies
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析响应数据
            response_data = response.json()
            self.logger.info(f"价格查询请求成功，响应状态码: {response.status_code}")
            
            return response_data
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"请求失败: {str(e)}")
            raise
        except json.JSONDecodeError as e:
            self.logger.error(f"响应数据解析失败: {str(e)}")
            raise
        except Exception as e:
            self.logger.error(f"发送价格请求时发生未知错误: {str(e)}")
            raise
    
    def save_response_to_file(self, response_data: Dict[str, Any], file_path: str) -> None:
        """
        将响应数据保存到文件
        
        Args:
            response_data: 响应数据
            file_path: 文件保存路径
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(response_data, f, ensure_ascii=False, indent=4)
            self.logger.info(f"响应数据已保存到: {file_path}")
        except Exception as e:
            self.logger.error(f"保存响应数据失败: {str(e)}")
            raise
    
    def get_hotel_price(self, 
                        hotel_id: str, 
                        room_type: str = "0002",
                        start_date: str = "2025-07-22",
                        end_date: str = "2025-10-20",
                        save_to_file: bool = True,
                        file_path: str = "同程酒店数据.json") -> Dict[str, Any]:
        """
        获取酒店价格信息的完整流程
        
        Args:
            hotel_id: 酒店ID
            room_type: 房型代码
            start_date: 开始日期
            end_date: 结束日期
            save_to_file: 是否保存到文件
            file_path: 保存文件路径
            
        Returns:
            Dict[str, Any]: 价格数据
        """
        try:
            # 发送价格查询请求
            response_data = self.send_price_request(
                hotel_id=hotel_id,
                room_type=room_type,
                start_date=start_date,
                end_date=end_date
            )
            
            # 保存到文件
            if save_to_file:
                self.save_response_to_file(response_data, file_path)
            
            return response_data
            
        except Exception as e:
            self.logger.error(f"获取酒店价格失败: {str(e)}")
            raise





if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    sender = TongchengHotelRequestSender(logging.getLogger(__name__))
    hotel_id: str = '26964091'
    room_type: str = "0012"
    start_date: str = "2025-07-23"
    end_date: str = "2025-10-20"
    
    try:
        result = sender.send_price_request(hotel_id, room_type, start_date, end_date)
        print(f"请求成功，响应数据: {result}")
    except Exception as e:
        print(f"请求失败: {str(e)}")