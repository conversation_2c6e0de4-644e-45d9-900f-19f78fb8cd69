import requests
import json
import subprocess
from functools import partial
import logging
from typing import Dict, Any, Optional
from tongcheng.config.browser_config import BrowserConfig
import time


# 设置subprocess编码
subprocess.Popen = partial(subprocess.Popen, encoding='utf-8')
import execjs



class RoomRelationshipGetter:
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)

        # 默认请求头
        self.default_headers = BrowserConfig.send_hotel_request.headers

        # 默认cookies
        self.default_cookies = BrowserConfig.send_hotel_request.cookies

        # API端点
        self.price_api_url = "https://ebooking.elong.com/product/roomPrice/ajaxRoomRateList"

        # JS文件路径
        self.js_file_path = BrowserConfig.encrypt_js_path
        
        # 重试配置
        self.max_retries = 3
        self.retry_delay = 2

    def _load_js_file(self) -> str:
        """
        加载JS加密文件

        Returns:
            str: JS文件内容
        """
        try:
            with open(self.js_file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            self.logger.error(f"JS文件未找到: {self.js_file_path}")
            raise
        except Exception as e:
            self.logger.error(f"读取JS文件失败: {str(e)}")
            raise

    def _generate_user_dun(self, data: Dict[str, Any], url: str) -> str:
        """
        生成user-dun加密值

        Args:
            data: 请求数据
            url: 请求URL

        Returns:
            str: 加密后的user-dun值
        """
        try:
            js_file = self._load_js_file()

            # 替换JS文件中的占位符
            js_file = js_file.replace("所有的data", json.dumps(data).replace(" ", ''))
            js_file = js_file.replace('当前url', url)

            # 设置环境变量以解决Windows编码问题
            import os
            os.environ['PYTHONIOENCODING'] = 'utf-8'

            # 执行JS代码
            user_dun = execjs.compile(js_file).call('run')
            self.logger.info(f"成功生成user-dun: {user_dun}")

            return user_dun['value']

        except Exception as e:
            self.logger.error(f"生成user-dun失败: {str(e)}")
            raise

    def _send_request_with_retry(self, data: Dict[str, Any], headers: Dict[str, str], 
                                cookies: Dict[str, str], max_retries: int = None) -> requests.Response:
        """
        发送请求并支持重试机制
        
        Args:
            data: 请求数据
            headers: 请求头
            cookies: cookies
            max_retries: 最大重试次数
            
        Returns:
            requests.Response: 响应对象
        """
        max_retries = max_retries or self.max_retries
        
        for attempt in range(max_retries + 1):
            try:
                self.logger.info(f"发送房型列表请求 (尝试 {attempt + 1}/{max_retries + 1})")
                
                response = requests.post(
                    self.price_api_url,
                    json=data,
                    headers=headers,
                    cookies=cookies,
                    timeout=30
                )
                
                # 检查响应状态
                response.raise_for_status()
                
                self.logger.info(f"房型列表请求成功，响应状态码: {response.status_code}")
                return response
                
            except requests.exceptions.Timeout:
                self.logger.warning(f"请求超时 (尝试 {attempt + 1}/{max_retries + 1})")
                if attempt < max_retries:
                    time.sleep(self.retry_delay)
                    continue
                else:
                    self.logger.error("请求超时，已达到最大重试次数")
                    raise
                    
            except requests.exceptions.ConnectionError:
                self.logger.warning(f"连接错误 (尝试 {attempt + 1}/{max_retries + 1})")
                if attempt < max_retries:
                    time.sleep(self.retry_delay)
                    continue
                else:
                    self.logger.error("连接错误，已达到最大重试次数")
                    raise
                    
            except requests.exceptions.RequestException as e:
                self.logger.error(f"请求异常: {str(e)}")
                if attempt < max_retries:
                    time.sleep(self.retry_delay)
                    continue
                else:
                    raise
                    
            except Exception as e:
                self.logger.error(f"未知错误: {str(e)}")
                raise

    def send_room_type_request(self, hotel_id: str, start_date: str, end_date: str) -> Optional[Dict[str, Any]]:
        """
        获取酒店的所有房型列表
        
        Args:
            hotel_id: 酒店ID
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            Optional[Dict[str, Any]]: 响应数据，失败时返回None
        """
        try:
            # 构建获取房型列表的请求数据
            data = {
                "hotelId": hotel_id,
                "productType": 0,
                "startDate": start_date,
                "endDate": end_date
            }
            
            self.logger.info(f"准备获取房型列表，酒店ID: {hotel_id}")
            
            # 生成user-dun
            user_dun = self._generate_user_dun(data, self.price_api_url)

            # 构建请求头
            headers = self.default_headers.copy()
            headers['user-dun'] = user_dun

            # 发送请求获取房型列表
            response = self._send_request_with_retry(data, headers, self.default_cookies)
            
            # 解析响应数据
            response_data = response.json()
            
            # 验证响应数据格式
            if not isinstance(response_data, dict):
                self.logger.error("响应数据格式错误，不是有效的JSON对象")
                return None
                
            # 检查响应数据是否包含必要字段
            if 'data' not in response_data:
                self.logger.error("响应数据缺少data字段")
                return None
                
            # 保存房型列表响应数据
            try:
                with open('酒店房型关系数据.json', 'w', encoding='utf-8') as f:
                    json.dump(response_data, f, ensure_ascii=False, indent=4)
                self.logger.info("房型列表响应数据已保存到文件")
            except Exception as e:
                self.logger.warning(f"保存响应数据到文件失败: {str(e)}")
            
            return response_data

        except requests.exceptions.RequestException as e:
            self.logger.error(f"发送房型列表请求失败: {str(e)}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"解析响应JSON失败: {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"发送房型列表请求时发生未知错误: {str(e)}")
            return None

    def parse_room_list_from_response(self, response_data: Dict[str, Any]) -> tuple[list, dict]:
        """
        从响应数据中解析出房型列表
        
        Args:
            response_data: 响应数据
            
        Returns:
            tuple[list, dict]: (房型ID列表, 房型名称映射字典)
        """
        try:
            room_list = []
            room_name_map = {}
            
            # 验证响应数据结构
            if not response_data or 'data' not in response_data:
                self.logger.error("响应数据格式错误，缺少data字段")
                return [], {}
                
            data = response_data.get('data', {})
            room_response = data.get('roomResponse', [])
            
            if not room_response:
                self.logger.warning("响应数据中没有找到房型信息")
                return [], {}
            
            # 解析房型信息
            for room in room_response:
                room_id = room.get('roomID')
                room_name = room.get('roomName', '')
                
                if room_id:
                    room_list.append(room_id)
                    room_name_map[room_id] = room_name
                    self.logger.debug(f"解析到房型: ID={room_id}, 名称={room_name}")
                else:
                    self.logger.warning(f"房型数据缺少roomID: {room}")
            
            self.logger.info(f"成功解析房型列表，共 {len(room_list)} 个房型")
            return room_list, room_name_map
            
        except Exception as e:
            self.logger.error(f"解析房型列表失败: {str(e)}")
            return [], {}

    def get_room_list(self, hotel_id: str, start_date: str, end_date: str) -> tuple[list, dict]:
        """
        获取房型列表的完整流程
        
        Args:
            hotel_id: 酒店ID
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            tuple[list, dict]: (房型ID列表, 房型名称映射字典)
        """
        self.logger.info(f"开始获取房型列表: 酒店ID={hotel_id}, 开始日期={start_date}, 结束日期={end_date}")
        
        try:
            # 发送请求获取房型数据
            response_data = self.send_room_type_request(hotel_id, start_date, end_date)
            
            if not response_data:
                self.logger.error("获取房型数据失败，返回空列表")
                return [], {}
            
            # 解析房型列表
            room_list, room_name_map = self.parse_room_list_from_response(response_data)
            
            if not room_list:
                self.logger.warning("解析房型列表为空，可能需要检查请求参数或网络连接")
                return [], {}
            
            self.logger.info(f"成功获取房型列表: {len(room_list)} 个房型")
            self.logger.debug(f"房型列表: {room_list}")
            self.logger.debug(f"房型名称映射: {room_name_map}")
            
            return room_list, room_name_map
            
        except Exception as e:
            self.logger.error(f"获取房型列表过程中发生错误: {str(e)}")
            return [], {}




