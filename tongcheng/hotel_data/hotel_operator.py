import time

class TongchengHotelOperator:
    def __init__(self, page, logger):
        self.page = page
        self.logger = logger

    def find_and_enter_hotel(self, hotel_name: str) -> bool:
        """查找并进入指定酒店页面"""
        try:
            self.logger.info(f"正在查找酒店: {hotel_name}")
            hotel_selector = self.page.ele('x://*[@id="common_app"]/div/div[2]/div/div[2]/span[1]/span/div/span')
            if hotel_selector:
                hotel_selector.click()
                time.sleep(0.8)  # 新增：等待备选项出现，确保页面加载完成
            else:
                self.logger.error("未找到酒店选择器")
                return False
            input_selector = 'x://*[@id="common_app"]/div/div[4]/div/div[2]/div/div[1]/div[5]/div/input'
            input_line = self.page.ele(input_selector)
            if input_line:
                input_line.click()
                input_line.input(hotel_name)
                time.sleep(0.8)  # 新增：等待备选项出现，确保页面加载完成
            else:
                self.logger.error("未找到酒店输入框")
                return False
            option_selector = '.el-tooltip option-text item'
            option = self.page.ele(option_selector)
            if option:
                option.click()
                time.sleep(0.8)
            else:
                self.logger.error("未找到酒店备选项")
                return False
            search_selector = 'x://*[@id="common_app"]/div/div[4]/div/div[2]/div/div[1]/button'
            search_button = self.page.ele(search_selector)
            if search_button:
                search_button.click()
                time.sleep(1)
            else:
                self.logger.error("未找到查询按钮")
                return False
            hotel_result_selector = '.w-1/3 pt-1.0 pb-1.0 text-14 cursor-pointer'
            hotel_result = self.page.ele(hotel_result_selector)
            if hotel_result:
                hotel_result.click()
                time.sleep(2)
                self.logger.info(f"成功进入酒店: {hotel_name}")
                return True
            else:
                self.logger.error("未找到查询结果中的酒店")
                return False
        except Exception as e:
            self.logger.error(f"查找酒店失败: {str(e)}")
            return False

    def click_price_maintain(self) -> bool:
        """点击房价维护按钮"""
        try:
            btn = self.page.ele('x://*[@id="common_app"]/div/div[3]/div[1]/div/div/div[2]/div[1]/div/ul[4]/li')
            if btn:
                btn.click()
                btn.click()
                time.sleep(2)
                return True
            else:
                self.logger.error("未找到房价维护按钮")
                return False
        except Exception as e:
            self.logger.error(f"点击房价维护按钮异常: {str(e)}")
            return False 